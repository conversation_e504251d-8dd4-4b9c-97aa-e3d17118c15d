import { ref, nextTick } from 'vue';
import { getNginxErrorLog } from '@/api/nginx';

// 页面引用
export const pageContainer = ref(null);

// 日志内容
export const logContent = ref('');

// 滚动位置
export const scrollTop = ref(0);

// 初始化错误日志数据
export const initErrorLogData = async () => {
	try {
		await loadLogContent();
	} catch (error) {
		console.error('初始化错误日志数据失败:', error);
		logContent.value = '加载日志失败，请检查网络连接或重试';
	}
};

// 滚动到底部
const scrollToBottom = async () => {
	await nextTick();
	// 设置一个很大的值来确保滚动到底部
	scrollTop.value = 999999;
};

// 加载日志内容
const loadLogContent = async () => {
	try {
		const response = await getNginxErrorLog();
		if (response.status) {
			// 如果返回的 msg 是日志内容
			logContent.value = response.msg || '暂无日志内容';
		} else {
			logContent.value = response.msg || '获取日志失败';
		}
		// 加载完成后滚动到底部
		await scrollToBottom();
	} catch (error) {
		console.error('加载日志内容失败:', error);
		logContent.value = '加载日志失败: ' + (error.msg || error.message || '未知错误');
	}
};

// 刷新日志
export const refreshLogs = async () => {
	try {
		pageContainer.value?.notify?.info('正在刷新日志...');
		await loadLogContent();
		pageContainer.value?.notify?.success('日志刷新成功');
	} catch (error) {
		console.error('刷新日志失败:', error);
		pageContainer.value?.notify?.error('刷新失败，请重试');
	}
};
